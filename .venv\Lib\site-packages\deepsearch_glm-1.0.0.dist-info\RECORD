deepsearch_glm-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
deepsearch_glm-1.0.0.dist-info/LICENSE,sha256=h3fWJ2rO6Es4aqZBmYhiXc82qEhxtLM3JAcKKoXTicA,1109
deepsearch_glm-1.0.0.dist-info/METADATA,sha256=B5BcU8VFUyaP5foCuUWH76NmtVwYdY0U8DQEtqtJxCc,11044
deepsearch_glm-1.0.0.dist-info/RECORD,,
deepsearch_glm-1.0.0.dist-info/WHEEL,sha256=mplBUCx-pBi3qWfkXZJ51YVNhldljO04tS33S9GKuiU,98
deepsearch_glm/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/__pycache__/__init__.cpython-312.pyc,,
deepsearch_glm/__pycache__/glm_create_from_docs.cpython-312.pyc,,
deepsearch_glm/__pycache__/glm_docqa.cpython-312.pyc,,
deepsearch_glm/__pycache__/glm_explore.cpython-312.pyc,,
deepsearch_glm/__pycache__/glm_query.cpython-312.pyc,,
deepsearch_glm/__pycache__/glm_utils.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_analyse_docs.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_apply_on_docs.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_apply_on_text.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_train_crf.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_train_tok.cpython-312.pyc,,
deepsearch_glm/__pycache__/nlp_utils.cpython-312.pyc,,
deepsearch_glm/andromeda_glm.cp312-win_amd64.pyd,sha256=RX_7ago62FVgW2J4BjdpTAy7KkNl4Eakcy5NhJYmW-8,5029376
deepsearch_glm/andromeda_nlp.cp312-win_amd64.pyd,sha256=d2JvbmzCE_LtoC2nuRFR7SZyi9lA9_aQh6Z7yWnNXlI,4890112
deepsearch_glm/andromeda_structs.cp312-win_amd64.pyd,sha256=F3wFzTFAQoFPm3b1SGy3BY1Kgt3u7Qh8QELU6ZtjoA8,4878848
deepsearch_glm/glm_create_from_docs.py,sha256=5oPCHjaO2wFBGFfZc6UujABdknzkUZreRvKz5Vw98VA,3377
deepsearch_glm/glm_docqa.py,sha256=iLvpMYdn3HtcLHpcBbQ6z5P_EBvcuJzFoFIO3VcTi5g,7449
deepsearch_glm/glm_explore.py,sha256=kijAnU5W-tvZ0v8RywBsm5MXn-VfWmww9ce21vqFY0E,1713
deepsearch_glm/glm_query.py,sha256=irrBJn_nc2-6ZT3_qIUSg1zZXbMiSYUhyrwQcGrBx_8,1902
deepsearch_glm/glm_utils.py,sha256=keKrh0fgqG__1hGl2JZ1IIbLsWDRSHFQgIS_Lz9Tg3U,10239
deepsearch_glm/libgcc_s_seh-1.dll,sha256=1tpM-hmVu1L0DawZCrNtsdD1017zD3iDIb4NnEIWYBg,101376
deepsearch_glm/libstdc++-6.dll,sha256=EqU3yFBFTMN7GESspSYuaXq0r_tZsiNvXVoxfzXfsqI,2020352
deepsearch_glm/libwinpthread-1.dll,sha256=4BuOhf1nwrhh9k1MzH32B1m1iapBAACij6K4SiCJF1s,54784
deepsearch_glm/nlp_analyse_docs.py,sha256=lwWTG3jh_pgvQfJpkd0d7V7YSPcPth94QCEcRoMU4_A,3893
deepsearch_glm/nlp_apply_on_docs.py,sha256=S75mIafLJPDkqLeVVEfE_Ae30oRsY-Yf6eT6N2g6fF8,7064
deepsearch_glm/nlp_apply_on_text.py,sha256=cPt6vhbIvE47k93nL2w75I6koY5x-M9aD2Ngcf0_pJg,2899
deepsearch_glm/nlp_model_training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/nlp_model_training/__pycache__/__init__.cpython-312.pyc,,
deepsearch_glm/nlp_model_training/__pycache__/name_classifier.cpython-312.pyc,,
deepsearch_glm/nlp_model_training/__pycache__/person_name_classifier.cpython-312.pyc,,
deepsearch_glm/nlp_model_training/__pycache__/reference_parsing.cpython-312.pyc,,
deepsearch_glm/nlp_model_training/__pycache__/semantic_classifier.cpython-312.pyc,,
deepsearch_glm/nlp_model_training/name_classifier.py,sha256=dapzzS0stJh7qGewhmT5pO3N5mnh35J-G2w5pSgqT2s,8562
deepsearch_glm/nlp_model_training/person_name_classifier.py,sha256=YGv4OFq0XzLnJXUUcdi5kLRn0fKtutycFsPuELwE6BI,6728
deepsearch_glm/nlp_model_training/reference_parsing.py,sha256=pPFe-yl5EVi599bNW2dCKIPMQet5yv8rHVSEbVuWp1s,11340
deepsearch_glm/nlp_model_training/semantic_classifier.py,sha256=kFG6kq98ZDL_uGC6Dc06-1o3TGZceBUcjcVXr_IKSQ8,15263
deepsearch_glm/nlp_train_crf.py,sha256=Ag7q2SCDIpf6pLtwz9Z8-DvvvEBilf4z-37LELRTTrA,5974
deepsearch_glm/nlp_train_tok.py,sha256=uINVRoCXw6p1cQl5e4TPUuTdFCwli-gtsarJCnFHxhA,1730
deepsearch_glm/nlp_utils.py,sha256=juiIOVi9zn2GTliWf1tJErtXX1AWzyeQvbot6wuzlyw,10803
deepsearch_glm/resources/confusables/confusablesRestricted.txt,sha256=3RagREluYkYr2NSbkmrmPzP--Wc9kjN28qXfBZQWV64,8725
deepsearch_glm/resources/confusables/confusablesSummary.txt,sha256=ZoPEkWXHtN2BE98SVKrfJt6Ld7Kqcwx4ZjyiSFjF6Sk,613990
deepsearch_glm/resources/confusables/note.txt,sha256=HY-LmcJfan8-2FphH_zSwKrBu2gIpNhZhq1CsF0iX_k,83
deepsearch_glm/resources/data.json,sha256=ExSaJawMCAMv_7-77eC2eakEdnVUiur8qvNS6ptT-6c,1771
deepsearch_glm/resources/data/nlp/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/data/text/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/data_nlp/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models.json,sha256=bUnhtLS1khLTimC1EhQ6kfHze_dMGCnCFKhP7NSYoe4,1073
deepsearch_glm/resources/models/crf/geoloc/note.md,sha256=yw0ub9PrNJ6RQ3x1vG_QfF_cugy6C_jWluI_UEtATHw,34
deepsearch_glm/resources/models/crf/part-of-speech/note.md,sha256=oNyTTlFYQTRfmG_LmQc3U8mfAklep8vduv33BNua1gQ,692
deepsearch_glm/resources/models/crf/reference/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/crf/ucmi/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/fasttext/language/note.md,sha256=stL41x1j1Gx7236ArKejMt7MUVyqdOAKadv5wBjbtDU,194
deepsearch_glm/resources/models/fasttext/metadata/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/fasttext/person-name/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/fasttext/semantic/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/fasttext/topic/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/resources/models/rgx/geoloc/note.md,sha256=yw0ub9PrNJ6RQ3x1vG_QfF_cugy6C_jWluI_UEtATHw,34
deepsearch_glm/resources/models/rgx/vau/units.jsonl,sha256=XifyzAT63XCunnirDWYh_KNVnKEVRvi7YkLrwfEedy8,122747
deepsearch_glm/resources/models/tok/default-tokenizer.model,sha256=nlVq_UQhO2vRviuFDru9mPVIFDeoAhr69Y7n-xgY00c,499723
deepsearch_glm/resources/models/tok/note.md,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepsearch_glm/utils/__pycache__/__init__.cpython-312.pyc,,
deepsearch_glm/utils/__pycache__/common.cpython-312.pyc,,
deepsearch_glm/utils/__pycache__/doc_utils.cpython-312.pyc,,
deepsearch_glm/utils/__pycache__/ds_query.cpython-312.pyc,,
deepsearch_glm/utils/__pycache__/ds_utils.cpython-312.pyc,,
deepsearch_glm/utils/__pycache__/load_pretrained_models.cpython-312.pyc,,
deepsearch_glm/utils/common.py,sha256=njHbe_iPfzB0cifGm2Zcld2A33kLAMgWLwG1YgaZKiI,303
deepsearch_glm/utils/doc_utils.py,sha256=EozGETwibC93yJmnWvDjZgrAP9T_iImbA9c9cPkyzok,12693
deepsearch_glm/utils/ds_query.py,sha256=AS42dmR6ivImcnE9MCzEVtQLWTnuOE5J7GN_Xe0adiA,1915
deepsearch_glm/utils/ds_utils.py,sha256=ss3qF784JeZJVuw02izoLRDojgPucc_aYAnDpFR0d9k,8561
deepsearch_glm/utils/load_pretrained_models.py,sha256=lsZn7bKHZ8DNHtIUgidd09qkxeZEJKDtrOOoi95M_YY,3761
