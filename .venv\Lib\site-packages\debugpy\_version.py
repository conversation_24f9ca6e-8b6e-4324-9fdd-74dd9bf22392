
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-01-15T09:42:03-0800",
 "dirty": false,
 "error": null,
 "full-revisionid": "40a471e24aa9015e2b1a40449a24af3053f7566d",
 "version": "1.8.12"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
