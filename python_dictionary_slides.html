<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Python 字典课程 PPT</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        overflow: hidden;
      }

      .slide-container {
        width: 100vw;
        height: 100vh;
        display: flex;
        transition: transform 0.5s ease-in-out;
      }

      .slide {
        min-width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 60px;
        background: white;
        margin-right: 0;
        position: relative;
        overflow-y: auto;
      }

      .slide:nth-child(odd) {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }

      .slide:nth-child(even) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }

      .slide h1 {
        font-size: 3.5em;
        margin-bottom: 30px;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .slide h2 {
        font-size: 2.5em;
        margin-bottom: 25px;
        text-align: center;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .slide h3 {
        font-size: 2em;
        margin-bottom: 20px;
        color: #ffd700;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .slide p,
      .slide li {
        font-size: 1.4em;
        line-height: 1.6;
        margin-bottom: 15px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      }

      .slide ul {
        text-align: left;
        max-width: 900px;
      }

      .slide li {
        margin-bottom: 10px;
        padding-left: 10px;
      }

      .code-block {
        background: rgba(0, 0, 0, 0.8);
        color: #00ff00;
        padding: 20px;
        border-radius: 10px;
        font-family: "Courier New", monospace;
        font-size: 1.2em;
        margin: 20px 0;
        white-space: pre-wrap;
        max-width: 900px;
        width: 100%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      .output-block {
        background: rgba(255, 255, 255, 0.2);
        color: #ffd700;
        padding: 15px;
        border-radius: 8px;
        font-family: "Courier New", monospace;
        font-size: 1.1em;
        margin: 15px 0;
        border-left: 4px solid #ffd700;
        max-width: 900px;
        width: 100%;
      }

      .navigation {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
      }

      .nav-btn {
        background: rgba(255, 255, 255, 0.8);
        border: none;
        padding: 15px 25px;
        margin: 0 5px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 1.1em;
        font-weight: bold;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .nav-btn:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
      }

      .slide-number {
        position: absolute;
        top: 30px;
        right: 30px;
        background: rgba(255, 255, 255, 0.3);
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 1.1em;
      }

      .title-slide {
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 100%
        ) !important;
      }

      .intro-slide {
        background: linear-gradient(
          135deg,
          #f093fb 0%,
          #f5576c 100%
        ) !important;
      }

      .content-slide {
        background: linear-gradient(
          135deg,
          #4facfe 0%,
          #00f2fe 100%
        ) !important;
      }

      .highlight {
        background: rgba(255, 255, 0, 0.3);
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: bold;
      }

      .story-box {
        background: rgba(255, 255, 255, 0.2);
        padding: 25px;
        border-radius: 15px;
        margin: 20px 0;
        border-left: 5px solid #ffd700;
        max-width: 900px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container" id="slideContainer">
      <!-- 幻灯片 1: 标题页 -->
      <div class="slide title-slide">
        <div class="slide-number">1/13</div>
        <h1>第 6 章 字典 (Dictionary)</h1>
        <h2>Python的"瑞士军刀"</h2>
        <p style="font-size: 1.8em; margin-top: 40px">
          欢迎来到今天的 Python 课程！
        </p>
        <p style="font-size: 1.4em; margin-top: 30px">主讲人: [你的名字]</p>
        <p style="font-size: 1.4em">日期: 2025年6月19日</p>
        <p style="font-size: 1.2em; margin-top: 40px">
          课程时长: 45分钟 | 目标学员: 中文编程初学者
        </p>
      </div>

      <!-- 幻灯片 2: 导入 -->
      <div class="slide intro-slide">
        <div class="slide-number">2/13</div>
        <h2>故事引入：我们为什么需要字典？</h2>
        <div class="story-box">
          <p>
            想象一下，我们想用程序记录一个人的信息。如果用列表，可能是这样的：
          </p>
          <div class="code-block">['张三', '25', '北京']</div>
          <p>
            <span class="highlight">问题来了</span>：'25'
            是什么？'北京'又是什么？我们必须记住每个位置代表的含义，非常麻烦。
          </p>
        </div>
        <h3>如果有一种方法，能给每个数据贴上一个标签就好了！</h3>
        <ul>
          <li>'姓名' → '张三'</li>
          <li>'年龄' → 25</li>
          <li>'城市' → '北京'</li>
        </ul>
        <p>
          这种"标签"和"信息"一一对应的组合，在Python里就叫<span
            class="highlight"
            >键(key)值(value)对</span
          >。而<span class="highlight">字典(Dictionary)</span
          >就是专门用来存放这种数据的"收纳盒"。
        </p>
      </div>

      <!-- 幻灯片 3: 一个简单的字典 -->
      <div class="slide content-slide">
        <div class="slide-number">3/13</div>
        <h2>6.1 一个简单的字典</h2>
        <h3>字典的基本结构：</h3>
        <ul>
          <li>字典由花括号 <span class="highlight">{}</span> 包围</li>
          <li>里面包含一系列的键值对</li>
          <li>
            每个键(key)和它的值(value)用冒号
            <span class="highlight">:</span> 分隔
          </li>
          <li>键值对之间用逗号 <span class="highlight">,</span> 分隔</li>
        </ul>
        <h3>代码示例：创建一个描述外星人的字典</h3>
        <div class="code-block">
          # alien.py alien_0 = {'color': 'green', 'points': 5} print(alien_0)
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">{'color': 'green', 'points': 5}</div>
      </div>

      <!-- 幻灯片 4: 访问字典中的值 -->
      <div class="slide content-slide">
        <div class="slide-number">4/13</div>
        <h2>6.2.1 访问字典中的值</h2>
        <p>要获取与键相关联的值，可以依次指定字典名和放在方括号[]内的键。</p>
        <p>
          <span class="highlight"
            >这就像根据标签名字，从收纳盒里取出对应的东西。</span
          >
        </p>
        <h3>代码示例：获取外星人的颜色和分数</h3>
        <div class="code-block">
          # access_value.py alien_0 = {'color': 'green', 'points': 5}
          alien_color = alien_0['color'] alien_points = alien_0['points']
          print(f"这个外星人的颜色是: {alien_color}") print(f"你击杀它会得到
          {alien_points} 分!")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">
          这个外星人的颜色是: green 你击杀它会得到 5 分!
        </div>
      </div>

      <!-- 幻灯片 5: 添加键值对 -->
      <div class="slide content-slide">
        <div class="slide-number">5/13</div>
        <h2>6.2.2 添加键值对</h2>
        <ul>
          <li>
            字典是<span class="highlight">动态的</span>，可以随时添加新的键值对
          </li>
          <li>
            语法:
            <span class="highlight"
              >dictionary_name['new_key'] = new_value</span
            >
          </li>
        </ul>
        <h3>代码示例：给我们的外星人添加坐标位置信息</h3>
        <div class="code-block">
          # add_key_value.py alien_0 = {'color': 'green', 'points': 5}
          print(f"修改前: {alien_0}") alien_0['x_position'] = 0
          alien_0['y_position'] = 25 print(f"修改后: {alien_0}")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">
          修改前: {'color': 'green', 'points': 5} 修改后: {'color': 'green',
          'points': 5, 'x_position': 0, 'y_position': 25}
        </div>
      </div>

      <!-- 幻灯片 6: 从创建一个空字典开始 -->
      <div class="slide content-slide">
        <div class="slide-number">6/13</div>
        <h2>6.2.3 从创建一个空字典开始</h2>
        <p>
          有时候，我们需要一个<span class="highlight">"空的收纳盒"</span
          >，然后再根据需要一件件地往里放东西。
        </p>
        <h3>代码示例：先创建一个空的用户档案，然后逐步填入信息</h3>
        <div class="code-block">
          # empty_dict.py user_profile = {} print(f"初始字典: {user_profile}")
          user_profile['name'] = '张三' user_profile['level'] = 5
          print(f"添加信息后: {user_profile}")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">
          初始字典: {} 添加信息后: {'name': '张三', 'level': 5}
        </div>
      </div>

      <!-- 幻灯片 7: 修改字典中的值 -->
      <div class="slide content-slide">
        <div class="slide-number">7/13</div>
        <h2>6.2.4 修改字典中的值</h2>
        <p>
          要修改字典中的值，就像添加一样，指定字典名、键，然后赋予新值即可。
        </p>
        <h3>代码示例：外星人的颜色从绿色变成了黄色</h3>
        <div class="code-block">
          # modify_value.py alien_0 = {'color': 'green'}
          print(f"外星人原来的颜色是: {alien_0['color']}") alien_0['color'] =
          'yellow' print(f"外星人现在的颜色是: {alien_0['color']}")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">
          外星人原来的颜色是: green 外星人现在的颜色是: yellow
        </div>
      </div>

      <!-- 幻灯片 8: 删除键值对 -->
      <div class="slide content-slide">
        <div class="slide-number">8/13</div>
        <h2>6.2.5 删除键值对</h2>
        <ul>
          <li>
            使用 <span class="highlight">del</span> 语句可以彻底删除一个键值对
          </li>
          <li>被删除的键值对将<span class="highlight">永久消失</span></li>
        </ul>
        <h3>代码示例：我们不再需要记录外星人的分数了</h3>
        <div class="code-block">
          # delete_key_value.py alien_0 = {'color': 'green', 'points': 5}
          print(f"删除前: {alien_0}") del alien_0['points'] print(f"删除后:
          {alien_0}")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">
          删除前: {'color': 'green', 'points': 5} 删除后: {'color': 'green'}
        </div>
      </div>

      <!-- 幻灯片 9: 由类似的对象组成的字典 -->
      <div class="slide content-slide">
        <div class="slide-number">9/13</div>
        <h2>6.2.6 由类似的对象组成的字典</h2>
        <p>字典非常适合存储多个相似事物的信息。</p>
        <p>例如，记录很多同学喜欢的编程语言。</p>
        <div class="code-block">
          # favorite_languages.py favorite_languages = { '张三': 'python',
          '李四': 'c', '王五': 'java', } lang =
          favorite_languages['王五'].title() print(f"王五最喜欢的编程语言是
          {lang}。")
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">王五最喜欢的编程语言是 Java。</div>
        <p>
          <span class="highlight"
            >这个例子展示了字典如何优雅地存储具有相同结构的数据（人 →
            语言）。</span
          >
        </p>
      </div>

      <!-- 幻灯片 10: 使用 get() 来访问值 -->
      <div class="slide content-slide">
        <div class="slide-number">10/13</div>
        <h2>6.2.7 使用 get() 来访问值</h2>
        <div class="story-box">
          <p>
            <span class="highlight">重要提醒</span
            >：直接用方括号[]访问一个<strong>不存在</strong>的键会报错
            (KeyError)，导致程序崩溃！
          </p>
        </div>
        <h3>使用 get() 方法更安全：</h3>
        <ul>
          <li>如果键存在，返回对应的值</li>
          <li>
            如果键不存在，可以返回一个我们指定的默认值（默认为 None），而<span
              class="highlight"
              >不会报错</span
            >
          </li>
        </ul>
        <div class="code-block">
          # get_method.py alien_0 = {'color': 'green', 'speed': 'slow'} #
          下面这行代码会报错，因为没有'points'这个键 # print(alien_0['points'])
          # 使用 get() 就很安全 point_value = alien_0.get('points',
          '没有这个属性哦') print(point_value)
        </div>
        <h3>代码输出：</h3>
        <div class="output-block">没有这个属性哦</div>
      </div>

      <!-- 幻灯片 11: 实践练习 -->
      <div class="slide content-slide">
        <div class="slide-number">11/13</div>
        <h2>实践练习：创建学生信息字典</h2>
        <div class="story-box">
          <h3>任务：</h3>
          <p>创建一个字典来存储学生信息，包含姓名、年龄、专业和成绩。</p>
        </div>
        <h3>练习步骤：</h3>
        <ul>
          <li>1. 创建一个空字典 student_info</li>
          <li>2. 添加学生的基本信息</li>
          <li>3. 修改学生的成绩</li>
          <li>4. 使用 get() 方法安全地获取信息</li>
        </ul>
        <div class="code-block">
          # 练习代码 student_info = {} student_info['name'] = '小明'
          student_info['age'] = 20 student_info['major'] = '计算机科学'
          student_info['score'] = 85 print(f"学生信息: {student_info}") grade =
          student_info.get('grade', '暂无等级') print(f"等级: {grade}")
        </div>
      </div>

      <!-- 幻灯片 12: 字典的优势总结 -->
      <div class="slide intro-slide">
        <div class="slide-number">12/13</div>
        <h2>字典的优势总结</h2>
        <h3>为什么选择字典？</h3>
        <ul>
          <li><span class="highlight">可读性强</span>：键名直接说明数据含义</li>
          <li>
            <span class="highlight">灵活性高</span
            >：可以随时添加、修改、删除数据
          </li>
          <li>
            <span class="highlight">查找高效</span>：通过键直接访问值，速度很快
          </li>
          <li><span class="highlight">结构清晰</span>：适合存储相关联的数据</li>
          <li>
            <span class="highlight">安全访问</span>：get() 方法避免程序崩溃
          </li>
        </ul>
        <div class="story-box">
          <h3>记住这个比喻：</h3>
          <p>
            字典就像一个<span class="highlight">智能收纳盒</span
            >，每个物品都有清楚的标签，你可以快速找到想要的东西，还可以随时整理和更新内容！
          </p>
        </div>
      </div>

      <!-- 幻灯片 13: 课程总结 -->
      <div class="slide title-slide">
        <div class="slide-number">13/13</div>
        <h1>课程总结</h1>
        <h2>今天我们学会了：</h2>
        <ul style="font-size: 1.6em; line-height: 2">
          <li>✅ 字典的基本概念和语法</li>
          <li>✅ 创建、访问、修改字典</li>
          <li>✅ 添加和删除键值对</li>
          <li>✅ 使用 get() 方法安全访问</li>
          <li>✅ 字典在实际编程中的应用</li>
        </ul>
        <h2 style="margin-top: 50px; color: #ffd700">下节课预告</h2>
        <p style="font-size: 1.4em">
          我们将学习如何遍历字典，处理更复杂的数据结构！
        </p>
        <p style="font-size: 1.8em; margin-top: 40px">感谢大家的参与！🎉</p>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation">
      <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
      <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
      let currentSlide = 0;
      const totalSlides = 13;
      const slideContainer = document.getElementById("slideContainer");

      function showSlide(n) {
        if (n >= totalSlides) currentSlide = 0;
        if (n < 0) currentSlide = totalSlides - 1;

        const translateX = -currentSlide * 100;
        slideContainer.style.transform = `translateX(${translateX}vw)`;
      }

      function nextSlide() {
        currentSlide++;
        if (currentSlide >= totalSlides) currentSlide = 0;
        showSlide(currentSlide);
      }

      function previousSlide() {
        currentSlide--;
        if (currentSlide < 0) currentSlide = totalSlides - 1;
        showSlide(currentSlide);
      }

      // 键盘导航
      document.addEventListener("keydown", function (event) {
        if (event.key === "ArrowRight" || event.key === " ") {
          nextSlide();
        } else if (event.key === "ArrowLeft") {
          previousSlide();
        } else if (event.key >= "1" && event.key <= "9") {
          const slideNum = parseInt(event.key) - 1;
          if (slideNum < totalSlides) {
            currentSlide = slideNum;
            showSlide(currentSlide);
          }
        }
      });

      // 初始化
      showSlide(0);
    </script>
  </body>
</html>
