### **Python 字典课程**

课程时长: 45分钟

目标学员: 中文编程初学者

### **幻灯片 1: 标题页** **(1分钟)**

**要点:**

- **第 6 章 字典 (Dictionary)：Python的“瑞士军刀”**
- 欢迎来到今天的 Python 课程！
- 主讲人: [你的名字]
- 日期: 2025年6月19日

### **幻灯片 2: 导入 (3分钟)**

**要点:**

- **故事引入：我们为什么需要字典？**
- 想象一下，我们想用程序记录一个人的信息。如果用列表，可能是这样的：['张三', '25', '北京']。
- 问题来了：'25' 是什么？'北京'又是什么？我们必须记住每个位置代表的含义，非常麻烦。
- 如果有一种方法，能给每个数据贴上一个**标签**就好了！

- '姓名' -> '张三'
- '年龄' -> 25
- '城市' -> '北京'

- 这种“标签”和“信息”一一对应的组合，在Python里就叫**键(key)值(value)对**。而**字典(Dictionary)**就是专门用来存放这种数据的“收纳盒”。

**备注:**

- 用生活中“给东西贴标签”的例子，帮助学生快速理解“键值对”的核心概念，突出字典相比列表的优势。

### **幻灯片 3: 讲授 - 6.1 一个简单的字典** **(2分钟)**

**要点:**

- 字典由花括号{}包围。
- 里面包含一系列的键值对。
- 每个键(key)和它的值(value)用冒号:分隔。
- 键值对之间用逗号,分隔。

**代码示例:**

- 我们来创建一个描述外星人的字典。

\# alien.py
alien_0 = {'color': 'green', 'points': 5}

print(alien_0)



**代码输出:**

{'color': 'green', 'points': 5}



### **幻灯片 4: 讲授 - 6.2.1 访问字典中的值** **(2分钟)**

**要点:**

- 要获取与键相关联的值，可以依次指定字典名和放在方括号[]内的键。
- 这就像根据标签名字，从收纳盒里取出对应的东西。

**代码示例:**

- 获取外星人的颜色和分数。

\# access_value.py
alien_0 = {'color': 'green', 'points': 5}

alien_color = alien_0['color']
alien_points = alien_0['points']

print(f"这个外星人的颜色是: {alien_color}")
print(f"你击杀它会得到 {alien_points} 分!")



**代码输出:**

这个外星人的颜色是: green
你击杀它会得到 5 分!



### **幻灯片 5: 讲授 - 6.2.2 添加键值对** **(2分钟)**

**要点:**

- 字典是动态的，可以随时添加新的键值对。
- 语法: dictionary_name['new_key'] = new_value

**代码示例:**

- 给我们的外星人添加坐标位置信息。

\# add_key_value.py
alien_0 = {'color': 'green', 'points': 5}
print(f"修改前: {alien_0}")

alien_0['x_position'] = 0
alien_0['y_position'] = 25
print(f"修改后: {alien_0}")



**代码输出:**

修改前: {'color': 'green', 'points': 5}
修改后: {'color': 'green', 'points': 5, 'x_position': 0, 'y_position': 25}



### **幻灯片 6: 讲授 - 6.2.3 从创建一个空字典开始** **(2分钟)**

**要点:**

- 有时候，我们需要一个“空的收纳盒”，然后再根据需要一件件地往里放东西。

**代码示例:**

- 我们先创建一个空的用户档案，然后逐步填入信息。

\# empty_dict.py
user_profile = {}
print(f"初始字典: {user_profile}")

user_profile['name'] = '张三'
user_profile['level'] = 5
print(f"添加信息后: {user_profile}")



**代码输出:**

初始字典: {}
添加信息后: {'name': '张三', 'level': 5}



### **幻灯片 7: 讲授 - 6.2.4 修改字典中的值** **(2分钟)**

**要点:**

- 要修改字典中的值，就像添加一样，指定字典名、键，然后赋予新值即可。

**代码示例:**

- 外星人的颜色从绿色变成了黄色。

\# modify_value.py
alien_0 = {'color': 'green'}
print(f"外星人原来的颜色是: {alien_0['color']}")

alien_0['color'] = 'yellow'
print(f"外星人现在的颜色是: {alien_0['color']}")



**代码输出:**

外星人原来的颜色是: green
外星人现在的颜色是: yellow



### **幻灯片 8: 讲授 - 6.2.5 删除键值对** **(2分钟)**

**要点:**

- 使用del语句可以彻底删除一个键值对。
- 被删除的键值对将永久消失。

**代码示例:**

- 我们不再需要记录外星人的分数了。

\# delete_key_value.py
alien_0 = {'color': 'green', 'points': 5}
print(f"删除前: {alien_0}")

del alien_0['points']
print(f"删除后: {alien_0}")



**代码输出:**

删除前: {'color': 'green', 'points': 5}
删除后: {'color': 'green'}



### **幻灯片 9: 讲授 - 6.2.6 由类似的对象组成的字典** **(2分钟)**

**要点:**

- 字典非常适合存储多个相似事物的信息。
- 例如，记录很多同学喜欢的编程语言。

**代码示例:**

\# favorite_languages.py
favorite_languages = {
  '张三': 'python',
  '李四': 'c',
  '王五': 'java',
  }

lang = favorite_languages['王五'].title()
print(f"王五最喜欢的编程语言是 {lang}。")



**代码输出:**

王五最喜欢的编程语言是 Java。



**备注:**

- 这个例子展示了字典如何优雅地存储具有相同结构的数据（人 -> 语言）。

### **幻灯片 10: 讲授 - 6.2.7 使用 get() 来访问值** **(3分钟)**

**要点:**

- 直接用方括号[]访问一个**不存在**的键会报错 (KeyError)，导致程序崩溃！
- 使用 get() 方法更安全：

- 如果键存在，返回对应的值。
- 如果键不存在，可以返回一个我们指定的默认值（默认为 None），而**不会报错**。

**代码示例:**

- 查询一个可能不存在的属性：“分数”。

\# get_method.py
alien_0 = {'color': 'green', 'speed': 'slow'}

\# 下面这行代码会报错，因为没有'points'这个键
\# print(alien_0['points']) 

\# 使用 get() 就很安全
point_value = alien_0.get('points', '没有这个属性哦')
print(point_value)



**代码输出:**

没有这个属性哦



**备注:**

- 强调 get() 方法在处理不确定是否存在的数据时的重要性，能让程序更稳定、更健壮。