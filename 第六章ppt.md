# 第六章 字典 PPT

## 第1页：字典概述
### 字典的作用
- 字典让你能够将相关信息关联起来
- 可存储的信息量几乎不受限制
- 能够更准确地为各种真实物体建模

---

## 第2页：简单字典示例
### alien.py
```python
alien_0 = {'color': 'green', 'points': 5}
print(alien_0['color'])
print(alien_0['points'])
```
**输出：**
```
green
5
```

---

## 第3页：字典基本概念
### 什么是字典
- 字典是一系列键值对
- 每个键都与一个值关联
- 用花括号 {} 表示
- 键和值之间用冒号分隔，键值对之间用逗号分隔

```python
alien_0 = {'color': 'green', 'points': 5}
```

---

## 第4页：访问字典中的值
### 访问方法
```python
alien_0 = {'color': 'green'}
print(alien_0['color'])
```

### 实际应用
```python
alien_0 = {'color': 'green', 'points': 5}
new_points = alien_0['points']
print(f"You just earned {new_points} points!")
```

---

## 第5页：添加键值对
### 动态添加
```python
alien_0 = {'color': 'green', 'points': 5}
print(alien_0)

alien_0['x_position'] = 0
alien_0['y_position'] = 25
print(alien_0)
```
**字典会保留定义时的元素排列顺序**

---

## 第6页：创建空字典
### 从空字典开始
```python
alien_0 = {}
alien_0['color'] = 'green'
alien_0['points'] = 5
print(alien_0)
```
**适用于存储用户提供的数据或自动生成键值对**

---

## 第7页：修改字典中的值
### 修改值
```python
alien_0 = {'color': 'green'}
print(f"The alien is {alien_0['color']}.")

alien_0['color'] = 'yellow'
print(f"The alien is now {alien_0['color']}.")
```

---

## 第8页：根据条件修改值
### 动态修改示例
```python
alien_0 = {'x_position': 0, 'y_position': 25, 'speed': 'medium'}

if alien_0['speed'] == 'slow':
    x_increment = 1
elif alien_0['speed'] == 'medium':
    x_increment = 2
else:
    x_increment = 3

alien_0['x_position'] = alien_0['x_position'] + x_increment
```

---

## 第9页：删除键值对
### 使用 del 语句
```python
alien_0 = {'color': 'green', 'points': 5}
print(alien_0)

del alien_0['points']
print(alien_0)
```
**注意：删除的键值对永远消失了**

---

## 第10页：存储多个对象
### 字典存储多人信息
```python
favorite_languages = {
    'jen': 'python',
    'sarah': 'c',
    'edward': 'rust',
    'phil': 'python',
}

language = favorite_languages['sarah'].title()
print(f"Sarah's favorite language is {language}.")
```

---

## 第11页：使用 get() 方法
### 安全访问值
```python
alien_0 = {'color': 'green', 'speed': 'slow'}
point_value = alien_0.get('points', 'No point value assigned.')
print(point_value)
```
**避免键不存在时出现错误**

---

## 第12页：遍历所有键值对
### 使用 items() 方法
```python
user_0 = {
    'username': 'efermi',
    'first': 'enrico',
    'last': 'fermi',
}

for key, value in user_0.items():
    print(f"\nKey: {key}")
    print(f"Value: {value}")
```

---

## 第13页：遍历所有键
### 使用 keys() 方法
```python
favorite_languages = {
    'jen': 'python',
    'sarah': 'c',
    'edward': 'rust',
    'phil': 'python',
}

for name in favorite_languages.keys():
    print(name.title())
```

---

## 第14页：按顺序遍历键
### 使用 sorted() 函数
```python
favorite_languages = {
    'jen': 'python',
    'sarah': 'c',
    'edward': 'rust',
    'phil': 'python',
}

for name in sorted(favorite_languages.keys()):
    print(f"{name.title()}, thank you for taking the poll.")
```

---

## 第15页：遍历所有值
### 使用 values() 方法
```python
favorite_languages = {
    'jen': 'python',
    'sarah': 'c',
    'edward': 'rust',
    'phil': 'python',
}

for language in favorite_languages.values():
    print(language.title())
```

---

## 第16页：去除重复值
### 使用 set() 函数
```python
for language in set(favorite_languages.values()):
    print(language.title())
```
**集合中的每个元素都必须是独一无二的**

---

## 第17页：字典列表
### 存储多个字典
```python
alien_0 = {'color': 'green', 'points': 5}
alien_1 = {'color': 'yellow', 'points': 10}
alien_2 = {'color': 'red', 'points': 15}

aliens = [alien_0, alien_1, alien_2]

for alien in aliens:
    print(alien)
```

---

## 第18页：自动生成字典列表
### 使用循环创建
```python
aliens = []

for alien_number in range(30):
    new_alien = {'color': 'green', 'points': 5, 'speed': 'slow'}
    aliens.append(new_alien)

print(f"Total number of aliens: {len(aliens)}")
```

---

## 第19页：在字典中存储列表
### 比萨配料示例
```python
pizza = {
    'crust': 'thick',
    'toppings': ['mushrooms', 'extra cheese'],
}

print(f"You ordered a {pizza['crust']}-crust pizza "
      "with the following toppings:")

for topping in pizza['toppings']:
    print(f"\t{topping}")
```

---

## 第20页：多种语言偏好
### 每人多个值
```python
favorite_languages = {
    'jen': ['python', 'rust'],
    'sarah': ['c'],
    'edward': ['rust', 'go'],
    'phil': ['python', 'haskell'],
}

for name, languages in favorite_languages.items():
    print(f"\n{name.title()}'s favorite languages are:")
    for language in languages:
        print(f"\t{language.title()}")
```

---

## 第21页：在字典中存储字典
### 用户信息管理
```python
users = {
    'aeinstein': {
        'first': 'albert',
        'last': 'einstein',
        'location': 'princeton',
    },
    'mcurie': {
        'first': 'marie',
        'last': 'curie',
        'location': 'paris',
    },
}

for username, user_info in users.items():
    print(f"\nUsername: {username}")
    full_name = f"{user_info['first']} {user_info['last']}"
    location = user_info['location']
    print(f"\tFull name: {full_name.title()}")
    print(f"\tLocation: {location.title()}")
```

---

## 第22页：小结
### 字典的核心功能
- 定义和使用字典存储信息
- 访问和修改字典元素
- 遍历字典（键值对、键、值）
- 嵌套：列表中的字典、字典中的列表、字典中的字典
- 字典是动态结构，可随时添加、修改、删除键值对
