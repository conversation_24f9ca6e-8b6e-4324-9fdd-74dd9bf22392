{"title": "ExportedCCSDocument", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "default": "pdf-document", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "description": {"$ref": "#/definitions/CCSDocumentDescription"}, "file-info": {"$ref": "#/definitions/CCSFileInfoObject"}, "main-text": {"title": "Main-Text", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/Ref"}, {"$ref": "#/definitions/RecursiveList"}, {"$ref": "#/definitions/BaseText"}]}}, "figures": {"title": "Figures", "type": "array", "items": {"$ref": "#/definitions/BaseCell"}}, "tables": {"title": "Tables", "type": "array", "items": {"$ref": "#/definitions/Table"}}, "bitmaps": {"title": "Bitmaps", "type": "array", "items": {"$ref": "#/definitions/BitmapObject"}}, "equations": {"title": "Equations", "type": "array", "items": {"$ref": "#/definitions/BaseCell"}}, "footnotes": {"title": "Footnotes", "type": "array", "items": {"$ref": "#/definitions/BaseText"}}, "page-dimensions": {"title": "Page-Dimensions", "type": "array", "items": {"$ref": "#/definitions/PageDimensions"}}, "page-footers": {"title": "Page-Footers", "type": "array", "items": {"$ref": "#/definitions/BaseText"}}, "page-headers": {"title": "Page-Headers", "type": "array", "items": {"$ref": "#/definitions/BaseText"}}, "_s3_data": {"$ref": "#/definitions/S3Data"}}, "required": ["name", "description", "file-info", "main-text"], "definitions": {"Affiliation": {"title": "Affiliation", "type": "object", "properties": {"name": {"title": "Name", "x-es": {"fields": {"lower": {"normalizer": "lowercase_asciifolding", "type": "keyword"}, "keyword": {"type": "keyword"}}, "type": "keyword"}, "type": "string"}, "id": {"title": "Id", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "source": {"title": "Source", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}}, "required": ["name", "id", "source"]}, "Author": {"title": "Author", "type": "object", "properties": {"name": {"title": "Name", "x-es": {"fields": {"lower": {"normalizer": "lowercase_asciifolding", "type": "keyword"}, "keyword": {"type": "keyword"}}, "type": "keyword"}, "type": "string"}, "id": {"title": "Id", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "source": {"title": "Source", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "affiliations": {"title": "Affiliations", "type": "array", "items": {"$ref": "#/definitions/Affiliation"}}}, "required": ["name", "id", "source", "affiliations"]}, "Identifier": {"title": "Identifier", "type": "object", "properties": {"type": {"title": "Type", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "value": {"title": "Value", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "_name": {"title": " Name", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}}, "required": ["type", "value", "_name"]}, "Log": {"title": "Log", "type": "object", "properties": {"agent": {"title": "Agent", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "type": {"title": "Type", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "comment": {"title": "Comment", "type": "string"}, "date": {"title": "Date", "x-es": {"type": "date"}, "type": "string", "format": "date-time"}}, "required": ["agent", "type", "comment", "date"]}, "CCSDocumentDescription": {"title": "CCSDocumentDescription", "type": "object", "properties": {"title": {"title": "Title", "type": "string"}, "abstract": {"title": "Abstract", "type": "array", "items": {"type": "string"}}, "authors": {"title": "Authors", "type": "array", "items": {"$ref": "#/definitions/Author"}}, "affiliations": {"title": "Affiliations", "type": "array", "items": {"$ref": "#/definitions/Affiliation"}}, "subjects": {"title": "Subjects", "x-es": {"fields": {"keyword": {"ignore_above": 8191, "type": "keyword"}}}, "type": "array", "items": {"type": "string"}}, "keywords": {"title": "Keywords", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "array", "items": {"type": "string"}}, "publication_date": {"title": "Publication Date", "x-es": {"type": "date"}, "type": "string", "format": "date-time"}, "languages": {"title": "Languages", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "array", "items": {"type": "string"}}, "publishers": {"title": "Publishers", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "array", "items": {"type": "string"}}, "url_refs": {"title": "<PERSON><PERSON>", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "array", "items": {"type": "string"}}, "references": {"title": "References", "type": "array", "items": {"$ref": "#/definitions/Identifier"}}, "advanced": {"title": "Advanced", "additionalProperties": {}, "properties": {}, "type": "object"}, "analytics": {"title": "Analytics", "additionalProperties": {}, "properties": {}, "type": "object"}, "logs": {"title": "Logs", "type": "array", "items": {"$ref": "#/definitions/Log"}}}, "required": ["title", "abstract", "authors", "affiliations", "subjects", "keywords", "publication_date", "languages", "publishers", "url_refs", "references", "advanced", "analytics", "logs"]}, "CCSFileInfoDescription": {"title": "CCSFileInfoDescription", "type": "object", "properties": {"author": {"title": "Author", "type": "array", "items": {"type": "string"}}, "keywords": {"title": "Keywords", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "title": {"title": "Title", "type": "string"}}}, "PageReference": {"title": "PageReference", "type": "object", "properties": {"hash": {"title": "Hash", "type": "string"}, "model": {"title": "Model", "type": "string"}, "page": {"title": "Page", "type": "integer"}}, "required": ["hash", "model", "page"]}, "CCSFileInfoObject": {"title": "CCSFileInfoObject", "type": "object", "properties": {"filename": {"title": "Filename", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "#-pages": {"title": "#-Pages", "type": "integer"}, "document-hash": {"title": "Document-Hash", "type": "string"}, "collection-name": {"title": "Collection-Name", "type": "string"}, "description": {"$ref": "#/definitions/CCSFileInfoDescription"}, "page-hashes": {"title": "Page<PERSON><PERSON><PERSON>", "type": "array", "items": {"$ref": "#/definitions/PageReference"}}, "filename-prov": {"title": "Filename-Prov", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}}, "required": ["filename", "#-pages", "document-hash", "collection-name", "description", "page-hashes", "filename-prov"]}, "Ref": {"title": "Ref", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "__ref": {"title": "  Ref", "type": "string"}}, "required": ["name", "type", "__ref"]}, "Prov": {"title": "Prov", "type": "object", "properties": {"bbox": {"title": "Bbox", "type": "array", "items": [{"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "number"}]}, "page": {"title": "Page", "type": "integer"}, "span": {"title": "Span", "type": "array", "items": [{"type": "integer"}, {"type": "integer"}]}}, "required": ["bbox", "page", "span"]}, "ListItem": {"title": "ListItem", "type": "object", "properties": {"text": {"title": "Text", "type": "string"}, "type": {"title": "Type", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "name": {"title": "Name", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "font": {"title": "Font", "type": "string"}, "prov": {"title": "Prov", "type": "array", "items": {"$ref": "#/definitions/Prov"}}, "identifier": {"title": "Identifier", "type": "string"}}, "required": ["text", "type", "identifier"]}, "RecursiveList": {"title": "RecursiveList", "type": "object", "properties": {"data": {"title": "Data", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/ListItem"}, {"$ref": "#/definitions/RecursiveList"}]}}, "name": {"title": "Name", "type": "string"}, "prov": {"title": "Prov", "type": "array", "items": {"$ref": "#/definitions/Prov"}}, "type": {"title": "Type", "type": "string"}}, "required": ["data", "type"]}, "BaseText": {"title": "BaseText", "type": "object", "properties": {"text": {"title": "Text", "type": "string"}, "type": {"title": "Type", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "name": {"title": "Name", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}, "font": {"title": "Font", "type": "string"}, "prov": {"title": "Prov", "type": "array", "items": {"$ref": "#/definitions/Prov"}}}, "required": ["text", "type"]}, "BoundingBoxContainer": {"title": "BoundingBoxContainer", "type": "object", "properties": {"min": {"title": "Min", "type": "array", "items": [{"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "number"}]}, "max": {"title": "Max", "type": "array", "items": [{"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "number"}]}}, "required": ["min", "max"]}, "CellsContainer": {"title": "CellsContainer", "type": "object", "properties": {"data": {"title": "Data", "type": "array", "items": {"type": "array", "items": [{"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "string"}, {"type": "string"}]}}, "header": {"title": "Header", "default": ["x0", "y0", "x1", "y1", "font", "text"], "type": "array", "items": [{"enum": ["x0"], "type": "string"}, {"enum": ["y0"], "type": "string"}, {"enum": ["x1"], "type": "string"}, {"enum": ["y1"], "type": "string"}, {"enum": ["font"], "type": "string"}, {"enum": ["text"], "type": "string"}]}}, "required": ["data"]}, "BaseCell": {"title": "BaseCell", "type": "object", "properties": {"bounding-box": {"$ref": "#/definitions/BoundingBoxContainer"}, "cells": {"$ref": "#/definitions/CellsContainer"}, "prov": {"title": "Prov", "type": "array", "items": {"$ref": "#/definitions/Prov"}}, "text": {"title": "Text", "type": "string"}, "type": {"title": "Type", "x-es": {"type": "keyword", "ignore_above": 8191}, "type": "string"}}, "required": ["bounding-box", "cells", "text", "type"]}, "TableCell": {"title": "TableCell", "type": "object", "properties": {"bbox": {"title": "Bbox", "type": "array", "items": [{"type": "number"}, {"type": "number"}, {"type": "number"}, {"type": "number"}]}, "spans": {"title": "Spans", "type": "array", "items": {"type": "array", "items": [{"type": "integer"}, {"type": "integer"}]}}, "text": {"title": "Text", "type": "string"}, "type": {"title": "Type", "type": "string"}}, "required": ["text", "type"]}, "Table": {"title": "Table", "type": "object", "properties": {"num_cols": {"title": "<PERSON>um <PERSON>s", "type": "integer"}, "num_rows": {"title": "Num Rows", "type": "integer"}, "bounding_box": {"$ref": "#/definitions/BoundingBoxContainer"}, "cells": {"$ref": "#/definitions/CellsContainer"}, "data": {"title": "Data", "type": "array", "items": {"type": "array", "items": {"$ref": "#/definitions/TableCell"}}}, "model": {"title": "Model", "type": "string"}, "prov": {"$ref": "#/definitions/Prov"}, "text": {"title": "Text", "type": "string"}, "type": {"title": "Type", "type": "string"}}, "required": ["num_cols", "num_rows", "data", "text", "type"]}, "BitmapObject": {"title": "BitmapObject", "type": "object", "properties": {"type": {"title": "Type", "type": "string"}, "bounding_box": {"$ref": "#/definitions/BoundingBoxContainer"}, "prov": {"$ref": "#/definitions/Prov"}}, "required": ["type", "bounding_box", "prov"]}, "PageDimensions": {"title": "PageDimensions", "type": "object", "properties": {"height": {"title": "Height", "type": "number"}, "page": {"title": "Page", "type": "integer"}, "width": {"title": "<PERSON><PERSON><PERSON>", "type": "number"}}, "required": ["height", "page", "width"]}, "S3Resource": {"title": "S3Resource", "type": "object", "properties": {"mime": {"title": "<PERSON><PERSON>", "type": "string"}, "path": {"title": "Path", "type": "string"}, "page": {"title": "Page", "type": "integer"}}, "required": ["mime", "path"]}, "S3Data": {"title": "S3Data", "type": "object", "properties": {"pdf_document": {"title": "Pdf Document", "type": "array", "items": {"$ref": "#/definitions/S3Resource"}}, "pdf_pages": {"title": "Pdf Pages", "type": "array", "items": {"$ref": "#/definitions/S3Resource"}}}}}}